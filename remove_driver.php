<?php
require_once 'db_connect.php';

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$driver_id = $_GET['id'];

// Delete driver
$sql = "DELETE FROM drivers WHERE id = ? AND user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ii", $driver_id, $_SESSION['user_id']);

if ($stmt->execute()) {
    header("Location: index.php");
    exit();
} else {
    echo '<!DOCTYPE html><html lang="bn"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0">'
        .'<title>ড্রাইভার অপসারণ ত্রুটি</title>'
        .'<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">'
        .'<style>:root {--purple-700: #3a006a;--purple-800: #2d003e;--white: #fff;}body {background: linear-gradient(135deg, var(--purple-700) 0%, var(--purple-800) 100%);color: var(--white);padding-top: 70px;font-family: Noto Sans Bengali, SolaimanLipi, Arial, sans-serif;} .card{max-width:400px;margin:80px auto;background:rgba(255,255,255,0.1);color:#fff;border-radius:1rem;box-shadow:0 4px 16px rgba(0,0,0,0.2);} .card-header{background:linear-gradient(135deg,var(--purple-700),var(--purple-800));color:#fff;font-weight:600;} .btn{background:linear-gradient(135deg,var(--purple-700),var(--purple-800));color:#fff;border:none;} .btn:hover{background:linear-gradient(135deg,var(--purple-800),var(--purple-700));}</style></head><body>'
        .'<div class="container"><div class="card"><div class="card-header text-center">ত্রুটি</div><div class="card-body text-center"><p>ড্রাইভার অপসারণে সমস্যা হয়েছে:</p><div class="alert alert-danger">'.htmlspecialchars($stmt->error).'</div><a href="index.php" class="btn">ড্যাশবোর্ডে ফিরে যান</a></div></div></div></body></html>';
}
$stmt->close();
$conn->close();
?>