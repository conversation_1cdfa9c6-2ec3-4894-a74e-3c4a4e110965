<?php
session_start();
require_once 'db_connect.php';

// Check if user is already logged in
if(isset($_SESSION['user_id'])) {
    header("Location: index.php");
    exit();
}

$error = '';

// Process login form
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = $_POST['username'];
    $password = $_POST['password'];
    
    // Validate input
    if(empty($username) || empty($password)) {
        $error = "সব ঘর পূরণ করুন";
    } else {
        // Check user credentials
        $sql = "SELECT id, username, password FROM users WHERE username = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if($result->num_rows == 1) {
            $user = $result->fetch_assoc();
            
            // Verify password
            if(password_verify($password, $user['password'])) {
                // Password is correct, start a new session
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                
                // Redirect to dashboard
                header("Location: index.php");
                exit();
            } else {
                $error = "ইউজারনেম অথবা পাসওয়ার্ড ভুল";
            }
        } else {
            $error = "ইউজারনেম অথবা পাসওয়ার্ড ভুল";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>লগইন - প্রচেষ্টা অটো রিকশা</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="futuristic-background.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Poppins', 'SolaimanLipi', Arial, sans-serif;
            background: linear-gradient(135deg, #3a006a 0%, #2d003e 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        /* Enhanced animated background */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
        }

        .particle.type1 {
            background: linear-gradient(45deg, rgba(58, 0, 106, 0.3), rgba(45, 0, 62, 0.3));
            box-shadow: 0 0 20px rgba(58, 0, 106, 0.5);
        }

        .particle.type2 {
            background: linear-gradient(45deg, rgba(45, 0, 62, 0.3), rgba(58, 0, 106, 0.3));
            box-shadow: 0 0 20px rgba(45, 0, 62, 0.5);
        }

        .particle.type3 {
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
        }

        .geometric-shape {
            position: absolute;
            border: 2px solid rgba(255, 255, 255, 0.1);
            animation: rotate 20s linear infinite;
        }

        .triangle {
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-bottom: 25px solid rgba(58, 0, 106, 0.2);
            animation: triangleFloat 12s ease-in-out infinite;
        }

        .square {
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, rgba(58, 0, 106, 0.2), rgba(45, 0, 62, 0.2));
            transform: rotate(45deg);
            animation: squareFloat 15s ease-in-out infinite;
        }

        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background: linear-gradient(180deg, transparent, rgba(255, 255, 255, 0.05));
            animation: wave 8s ease-in-out infinite;
        }

        .wave::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 200%;
            height: 100%;
            background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120'%3E%3Cpath d='M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z' fill='rgba(255,255,255,0.1)'/%3E%3C/svg%3E") repeat-x;
            animation: waveMove 10s linear infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg) scale(1);
                opacity: 0.7;
            }
            33% {
                transform: translateY(-30px) rotate(120deg) scale(1.1);
                opacity: 1;
            }
            66% {
                transform: translateY(-15px) rotate(240deg) scale(0.9);
                opacity: 0.8;
            }
        }

        @keyframes triangleFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-40px) rotate(180deg); }
        }

        @keyframes squareFloat {
            0%, 100% { transform: translateY(0px) rotate(45deg); }
            50% { transform: translateY(-25px) rotate(225deg); }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes wave {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes waveMove {
            0% { transform: translateX(0); }
            100% { transform: translateX(-50%); }
        }

        /* Glowing orbs */
        .glow-orb {
            position: absolute;
            border-radius: 50%;
            filter: blur(1px);
            animation: glowPulse 4s ease-in-out infinite;
        }

        @keyframes glowPulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.6;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        .login-container {
            width: 100%;
            max-width: 1200px;
            min-height: 700px;
            margin: 50px auto;
            position: relative;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
        }

        @media (max-width: 1300px) {
            .login-container {
                max-width: 98vw;
                min-height: 500px;
                padding: 20px 5px;
            }
        }
        @media (max-width: 900px) {
            .login-container {
                max-width: 100vw;
                min-height: 400px;
                flex-direction: column;
                padding: 10px 2px;
            }
        }
        @media (max-width: 600px) {
            .login-container {
                max-width: 100vw;
                min-height: 200px;
                flex-direction: column;
                padding: 2px 1px;
            }
        }

        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-header {
            background: linear-gradient(135deg, #3a006a, #2d003e);
            color: #fff;
            font-weight: 600;
            padding: 25px;
            text-align: center;
            border: none;
            position: relative;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .app-title {
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin: 0;
        }

        .card-body {
            padding: 40px 30px;
        }

        .form-label {
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control {
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus {
            border-color: #ff6b6b;
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 107, 0.25);
            background: white;
        }

        .btn-container {
            position: relative;
            height: 200px;
            margin-top: 20px;
            width: 100%;
            min-width: 300px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3a006a, #2d003e 80%);
            color: #fff;
            border: none;
            border-radius: 12px;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            position: absolute;
            top: 0;
            left: 0;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(58, 0, 106, 0.15);
        }

        .btn-primary:hover:not(.moving) {
            background: linear-gradient(135deg, #2d003e, #3a006a 80%);
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(58, 0, 106, 0.25);
        }

        .btn-primary.moving {
            cursor: not-allowed;
            animation: moveAway 0.5s ease-in-out;
        }

        @keyframes moveAway {
            0% { transform: translateX(0); }
            25% { transform: translateX(50px) rotate(10deg); }
            50% { transform: translateX(-50px) rotate(-10deg); }
            75% { transform: translateX(30px) rotate(5deg); }
            100% { transform: translateX(0) rotate(0deg); }
        }

        .btn-link {
            color: #3a006a;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-link:hover {
            color: #2d003e;
            text-decoration: underline;
        }

        .footer {
            position: fixed;
            left: 0;
            bottom: 0;
            width: 100vw;
            text-align: center;
            color: #8e2de2;
            background: rgba(255,255,255,0.95);
            font-size: 1rem;
            font-weight: 500;
            letter-spacing: 1px;
            padding: 12px 0 8px 0;
            z-index: 100;
            box-shadow: 0 -2px 12px rgba(142,45,226,0.07);
        }

        .alert-danger {
            border: none;
            border-radius: 12px;
            background: linear-gradient(135deg, #3a006a, #2d003e);
            color: #fff;
            font-weight: 500;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .login-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 30px;
            text-align: center;
        }

        /* Angel Wings Styles */
        .angel-wings {
            position: fixed;
            top: 50%;
            transform: translateY(-50%);
            z-index: 5;
            pointer-events: none;
        }

        .angel-wings.left {
            left: -50px;
            animation: wingFlutterLeft 3s ease-in-out infinite;
        }

        .angel-wings.right {
            right: -50px;
            animation: wingFlutterRight 3s ease-in-out infinite;
        }

        .wing {
            width: 120px;
            height: 200px;
            background: linear-gradient(45deg,
                rgba(255, 255, 255, 0.9) 0%,
                rgba(255, 255, 255, 0.7) 30%,
                rgba(240, 240, 240, 0.8) 60%,
                rgba(255, 255, 255, 0.6) 100%);
            border-radius: 60% 40% 60% 40%;
            position: relative;
            box-shadow:
                0 0 30px rgba(255, 255, 255, 0.5),
                inset 0 0 20px rgba(255, 255, 255, 0.3);
            filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.1));
        }

        .wing::before {
            content: '';
            position: absolute;
            top: 20%;
            left: 20%;
            width: 60%;
            height: 60%;
            background: linear-gradient(45deg,
                rgba(255, 255, 255, 0.4),
                rgba(240, 240, 240, 0.2));
            border-radius: 50% 30% 50% 30%;
            animation: wingGlow 2s ease-in-out infinite alternate;
        }

        .wing::after {
            content: '';
            position: absolute;
            top: 10%;
            left: 10%;
            width: 80%;
            height: 80%;
            background: radial-gradient(ellipse at center,
                rgba(255, 255, 255, 0.2) 0%,
                transparent 70%);
            border-radius: 60% 40% 60% 40%;
        }

        .angel-wings.left .wing {
            transform: rotate(-15deg);
        }

        .angel-wings.right .wing {
            transform: rotate(15deg) scaleX(-1);
        }

        @keyframes wingFlutterLeft {
            0%, 100% {
                transform: translateY(-50%) translateX(0) rotate(0deg);
            }
            25% {
                transform: translateY(-55%) translateX(10px) rotate(-5deg);
            }
            50% {
                transform: translateY(-45%) translateX(-5px) rotate(2deg);
            }
            75% {
                transform: translateY(-52%) translateX(8px) rotate(-3deg);
            }
        }

        @keyframes wingFlutterRight {
            0%, 100% {
                transform: translateY(-50%) translateX(0) rotate(0deg);
            }
            25% {
                transform: translateY(-55%) translateX(-10px) rotate(5deg);
            }
            50% {
                transform: translateY(-45%) translateX(5px) rotate(-2deg);
            }
            75% {
                transform: translateY(-52%) translateX(-8px) rotate(3deg);
            }
        }

        @keyframes wingGlow {
            0% {
                opacity: 0.6;
                transform: scale(1);
            }
            100% {
                opacity: 1;
                transform: scale(1.1);
            }
        }

        /* Enhanced button movement for more distance */
        .btn-primary.moving-far {
            cursor: not-allowed;
            animation: moveVeryFar 1s ease-in-out;
            z-index: 1000;
        }

        @keyframes moveVeryFar {
            0% { transform: translateX(0) translateY(0) rotate(0deg); }
            20% { transform: translateX(200px) translateY(-100px) rotate(45deg); }
            40% { transform: translateX(-250px) translateY(50px) rotate(-30deg); }
            60% { transform: translateX(180px) translateY(120px) rotate(60deg); }
            80% { transform: translateX(-200px) translateY(-80px) rotate(-45deg); }
            100% { transform: translateX(300px) translateY(-150px) rotate(90deg); }
        }

        /* Responsive design */
        @media (max-width: 576px) {
            .login-container {
                margin: 20px auto;
                max-width: 95%;
            }

            .card-body {
                padding: 30px 20px;
            }

            .app-title {
                font-size: 1.6rem;
            }

            .angel-wings {
                display: none; /* Hide wings on mobile for better UX */
            }

            .wing {
                width: 80px;
                height: 140px;
            }
        }

        @media (max-width: 768px) {
            .angel-wings.left {
                left: -30px;
            }

            .angel-wings.right {
                right: -30px;
            }

            .wing {
                width: 100px;
                height: 170px;
            }
        }
    </style>
</head>
<body>
    <!-- Futuristic 3D Background -->
    <?php include 'futuristic-background.html'; ?>

    <!-- Animated background -->
    <div class="bg-animation" id="bgAnimation"></div>

    <!-- Angel Wings -->
    <div class="angel-wings left">
        <div class="wing"></div>
    </div>
    <div class="angel-wings right">
        <div class="wing"></div>
    </div>

    <div class="container">
        <div class="login-container">
            <div class="card glass-card">
                <div class="card-header text-center">
                    <span class="app-title">প্রচেষ্টা অটো রিকশা</span>
                </div>
                <div class="card-body p-4">
                    <h4 class="text-center mb-4">লগইন করুন</h4>
                    
                    <?php if(!empty($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>
                    
                    <form method="post" action="" id="loginForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">ইউজারনেম</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">পাসওয়ার্ড</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>

                        <div class="btn-container">
                            <button type="submit" class="btn btn-primary btn-futuristic" id="loginBtn">লগইন</button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-3">
                        <a href="register.php" class="btn btn-link">নতুন অ্যাকাউন্ট তৈরি করুন</a>
                    </div>
                </div>
            </div>
            
            <div class="footer">
                <p>Developed by Mahtab Uddin Ahmed</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Create enhanced animated background
        function createEnhancedBackground() {
            const bgAnimation = document.getElementById('bgAnimation');

            // Create floating particles
            const particleCount = 40;
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                const types = ['type1', 'type2', 'type3'];
                const randomType = types[Math.floor(Math.random() * types.length)];
                particle.className = `particle ${randomType}`;

                // Random size between 6px and 20px
                const size = Math.random() * 14 + 6;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';

                // Random position
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';

                // Random animation delay and duration
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 6) + 's';

                bgAnimation.appendChild(particle);
            }

            // Create geometric shapes
            const shapeCount = 15;
            for (let i = 0; i < shapeCount; i++) {
                const shapes = ['triangle', 'square'];
                const randomShape = shapes[Math.floor(Math.random() * shapes.length)];
                const shape = document.createElement('div');
                shape.className = randomShape;

                // Random position
                shape.style.left = Math.random() * 100 + '%';
                shape.style.top = Math.random() * 100 + '%';

                // Random animation delay
                shape.style.animationDelay = Math.random() * 12 + 's';

                bgAnimation.appendChild(shape);
            }

            // Create glowing orbs
            const orbCount = 8;
            for (let i = 0; i < orbCount; i++) {
                const orb = document.createElement('div');
                orb.className = 'glow-orb';

                const size = Math.random() * 60 + 40;
                orb.style.width = size + 'px';
                orb.style.height = size + 'px';

                const colors = [
    'radial-gradient(circle, rgba(58,0,106,0.3) 0%, transparent 70%)',
    'radial-gradient(circle, rgba(45,0,62,0.3) 0%, transparent 70%)',
    'radial-gradient(circle, rgba(255,255,255,0.15) 0%, transparent 70%)'
];
                orb.style.background = colors[Math.floor(Math.random() * colors.length)];

                orb.style.left = Math.random() * 100 + '%';
                orb.style.top = Math.random() * 100 + '%';
                orb.style.animationDelay = Math.random() * 4 + 's';

                bgAnimation.appendChild(orb);
            }

            // Create wave effect
            const wave = document.createElement('div');
            wave.className = 'wave';
            bgAnimation.appendChild(wave);
        }

        // Check if there's an error and make button untouchable immediately
        let wrongAttempts = 0;
        let buttonUntouchable = false;

        <?php if(!empty($error)): ?>
            wrongAttempts++;
            makeButtonUntouchable();
        <?php endif; ?>

        function makeButtonUntouchable() {
            const loginBtn = document.getElementById('loginBtn');
            buttonUntouchable = true;

            // Make button absolutely positioned for movement
            loginBtn.style.position = 'absolute';
            loginBtn.style.pointerEvents = 'auto'; // Allow mouse events

            // Add mouseenter and click events to move button far away
            const btnContainer = document.querySelector('.btn-container');

            // Remove previous listeners by cloning
            const newBtn = loginBtn.cloneNode(true);
            loginBtn.parentNode.replaceChild(newBtn, loginBtn);

            newBtn.addEventListener('mouseenter', function() {
                if (buttonUntouchable) {
                    moveButtonRandomly();
                    showMessage('ভুল পাসওয়ার্ড! বাটন ধরতে পারবেন না! 😄');
                }
            });
            newBtn.addEventListener('click', function(e) {
                if (buttonUntouchable) {
                    e.preventDefault();
                    moveButtonRandomly();
                    showMessage('আগে সঠিক তথ্য দিন! 🤔');
                }
            });

            // Initial animation
            newBtn.classList.add('moving-far');
            setTimeout(() => {
                newBtn.classList.remove('moving-far');
            }, 1000);
        }

        function moveButtonRandomly() {
            const loginBtn = document.getElementById('loginBtn');
            const btnContainer = document.querySelector('.btn-container');

            // Get container and button sizes
            const containerRect = btnContainer.getBoundingClientRect();
            const btnRect = loginBtn.getBoundingClientRect();

            // Calculate max movement so button stays inside container
            const maxX = containerRect.width - btnRect.width;
            const maxY = containerRect.height - btnRect.height;

            // Pick a random far position (either left/right/top/bottom extremes)
            let randomX = Math.random() > 0.5 ? maxX - 10 : 10;
            let randomY = Math.random() > 0.5 ? maxY - 10 : 10;

            // Add some randomness
            randomX += (Math.random() - 0.5) * 40;
            randomY += (Math.random() - 0.5) * 40;

            // Clamp to container
            randomX = Math.max(0, Math.min(randomX, maxX));
            randomY = Math.max(0, Math.min(randomY, maxY));

            // Add playful rotation
            const randomRotation = (Math.random() - 0.5) * 60;

            loginBtn.style.transform = `translate(${randomX}px, ${randomY}px) rotate(${randomRotation}deg)`;
            loginBtn.style.transition = 'transform 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55)';

            // Add shake animation
            loginBtn.classList.add('moving-far');
            setTimeout(() => {
                loginBtn.classList.remove('moving-far');
            }, 1000);
        }

        function startRandomMovement() {
            if (buttonUntouchable) {
                // Move button randomly every 2-4 seconds
                const randomDelay = Math.random() * 2000 + 2000;
                setTimeout(() => {
                    if (buttonUntouchable) {
                        moveButtonRandomly();
                        startRandomMovement(); // Continue the cycle
                    }
                }, randomDelay);
            }
        }

        function showMessage(message, type = 'warning') {
            // Create temporary message
            const existingMsg = document.querySelector('.temp-message');
            if (existingMsg) existingMsg.remove();

            const msgDiv = document.createElement('div');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-warning';
            msgDiv.className = `alert ${alertClass} temp-message mt-3`;
            msgDiv.textContent = message;
            msgDiv.style.animation = type === 'success' ? 'fadeIn 0.5s ease-in-out' : 'shake 0.5s ease-in-out';
            msgDiv.style.borderRadius = '12px';
            msgDiv.style.fontWeight = '500';

            const form = document.getElementById('loginForm');
            form.appendChild(msgDiv);

            setTimeout(() => {
                if (msgDiv.parentNode) {
                    msgDiv.remove();
                }
            }, type === 'success' ? 2000 : 3000);
        }

        // Reset button when user starts typing correct info
        document.getElementById('username').addEventListener('input', resetButton);
        document.getElementById('password').addEventListener('input', resetButton);

        function resetButton() {
            if (buttonUntouchable) {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                // Reset if user has typed substantial content in both fields
                if (username.length > 3 && password.length > 4) {
                    const loginBtn = document.getElementById('loginBtn') || document.querySelector('.btn.btn-primary');
                    const btnContainer = document.querySelector('.btn-container');

                    // Reset button state
                    buttonUntouchable = false;
                    wrongAttempts = 0;

                    // Reset button position and properties
                    loginBtn.style.transform = 'translate(0, 0) rotate(0deg)';
                    loginBtn.style.position = 'absolute';
                    loginBtn.style.pointerEvents = 'auto';
                    loginBtn.style.transition = 'all 0.5s ease';

                    // Remove temp messages
                    const tempMsg = document.querySelector('.temp-message');
                    if (tempMsg) tempMsg.remove();

                    // Clone button to remove all event listeners
                    const newBtn = loginBtn.cloneNode(true);
                    loginBtn.parentNode.replaceChild(newBtn, loginBtn);

                    // Show success message
                    showMessage('এখন লগইন করতে পারেন! ✅', 'success');
                }
            }
        }

        // Initialize enhanced background when page loads
        document.addEventListener('DOMContentLoaded', function() {
            createEnhancedBackground();
        });

        // Add some interactive effects
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.3s ease';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>