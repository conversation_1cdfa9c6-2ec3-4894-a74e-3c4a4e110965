<?php
require_once 'db_connect.php';

// Create users table if it doesn't exist
$sql = "CREATE TABLE IF NOT EXISTS users (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

if ($conn->query($sql) === TRUE) {
    echo "Users table created successfully<br>";
} else {
    echo "Error creating users table: " . $conn->error . "<br>";
}

// Add user_id to rickshaws table
$sql = "CREATE TABLE IF NOT EXISTS rickshaws (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) UNSIGNED NOT NULL,
    rickshaw_number VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'Active',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
if ($conn->query($sql) === TRUE) {
    echo "Rickshaws table created/updated successfully<br>";
} else {
    echo "Error creating/updating rickshaws table: " . $conn->error . "<br>";
}

// Add user_id to drivers table
$sql = "CREATE TABLE IF NOT EXISTS drivers (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    nid VARCHAR(30) NOT NULL,
    rickshaw_id INT(11) UNSIGNED NOT NULL,
    rent DECIMAL(10,2) NOT NULL,
    join_date DATE NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (rickshaw_id) REFERENCES rickshaws(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
if ($conn->query($sql) === TRUE) {
    echo "Drivers table created/updated successfully<br>";
} else {
    echo "Error creating/updating drivers table: " . $conn->error . "<br>";
}

// Add user_id to attendance table
$sql = "CREATE TABLE IF NOT EXISTS attendance (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) UNSIGNED NOT NULL,
    driver_id INT(11) UNSIGNED NOT NULL,
    date DATE NOT NULL,
    status VARCHAR(20) NOT NULL,
    rent DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (driver_id) REFERENCES drivers(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
if ($conn->query($sql) === TRUE) {
    echo "Attendance table created/updated successfully<br>";
} else {
    echo "Error creating/updating attendance table: " . $conn->error . "<br>";
}

// Add user_id to rent_settings table
$sql = "CREATE TABLE IF NOT EXISTS rent_settings (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) UNSIGNED NOT NULL,
    day_type VARCHAR(20) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    special_date DATE DEFAULT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
if ($conn->query($sql) === TRUE) {
    echo "Rent settings table created/updated successfully<br>";
} else {
    echo "Error creating/updating rent settings table: " . $conn->error . "<br>";
}

// Check if admin user exists
$checkAdmin = "SELECT id FROM users WHERE username = 'admin'";
$result = $conn->query($checkAdmin);

if ($result && $result->num_rows == 0) {
    // Create default admin user
    $admin_username = 'admin';
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    
    $sql = "INSERT INTO users (username, password) VALUES (?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ss", $admin_username, $admin_password);
    
    if ($stmt->execute()) {
        echo "Default admin user created successfully<br>";
        echo "Username: admin<br>";
        echo "Password: admin123<br>";
        echo "<p>Please change this password after first login!</p>";
    } else {
        echo "Error creating default admin user: " . $stmt->error . "<br>";
    }
}

echo "<p><a href='login.php'>Go to login page</a></p>";

$conn->close();
?>
