/* Futuristic 3D Background with Moving Stars */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

:root {
    --primary-gold: #ffd700;
    --primary-black: #0a0a0a;
    --primary-purple: #3a006a;
    --accent-yellow: #ffeb3b;
    --accent-orange: #ff9800;
    --neon-blue: #00bcd4;
    --neon-pink: #e91e63;
    --glass-white: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
}

/* 3D Animated Background */
.futuristic-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    background: linear-gradient(135deg, 
        var(--primary-black) 0%, 
        #1a1a2e 25%, 
        var(--primary-purple) 50%, 
        #16213e 75%, 
        var(--primary-black) 100%);
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% {
        background: linear-gradient(135deg, 
            var(--primary-black) 0%, 
            #1a1a2e 25%, 
            var(--primary-purple) 50%, 
            #16213e 75%, 
            var(--primary-black) 100%);
    }
    50% {
        background: linear-gradient(135deg, 
            #0f0f23 0%, 
            var(--primary-purple) 25%, 
            #1a1a2e 50%, 
            var(--primary-black) 75%, 
            #16213e 100%);
    }
}

/* Moving Stars Layer 1 - Small Stars */
.stars-layer-1 {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, var(--primary-gold), transparent),
        radial-gradient(2px 2px at 40px 70px, var(--accent-yellow), transparent),
        radial-gradient(1px 1px at 90px 40px, var(--neon-blue), transparent),
        radial-gradient(1px 1px at 130px 80px, var(--primary-gold), transparent),
        radial-gradient(2px 2px at 160px 30px, var(--accent-orange), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: moveStars1 50s linear infinite;
}

@keyframes moveStars1 {
    from { transform: translateX(0) translateY(0); }
    to { transform: translateX(-200px) translateY(-100px); }
}

/* Moving Stars Layer 2 - Medium Stars */
.stars-layer-2 {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(3px 3px at 60px 20px, var(--neon-pink), transparent),
        radial-gradient(2px 2px at 100px 60px, var(--primary-gold), transparent),
        radial-gradient(3px 3px at 140px 90px, var(--neon-blue), transparent),
        radial-gradient(2px 2px at 180px 40px, var(--accent-yellow), transparent);
    background-repeat: repeat;
    background-size: 250px 150px;
    animation: moveStars2 70s linear infinite reverse;
}

@keyframes moveStars2 {
    from { transform: translateX(0) translateY(0) rotate(0deg); }
    to { transform: translateX(-250px) translateY(-150px) rotate(360deg); }
}

/* Moving Stars Layer 3 - Large Stars */
.stars-layer-3 {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(4px 4px at 80px 50px, var(--primary-gold), transparent),
        radial-gradient(3px 3px at 120px 20px, var(--accent-orange), transparent),
        radial-gradient(4px 4px at 200px 80px, var(--neon-blue), transparent);
    background-repeat: repeat;
    background-size: 300px 200px;
    animation: moveStars3 90s linear infinite;
}

@keyframes moveStars3 {
    from { transform: translateX(0) translateY(0) scale(1); }
    to { transform: translateX(-300px) translateY(-200px) scale(1.2); }
}

/* Floating Geometric Shapes */
.geometric-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.shape {
    position: absolute;
    opacity: 0.1;
    animation: floatShape 15s ease-in-out infinite;
}

.shape-1 {
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, var(--primary-gold), var(--accent-orange));
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-pink));
    border-radius: 50%;
    top: 60%;
    right: 15%;
    animation-delay: 5s;
}

.shape-3 {
    width: 120px;
    height: 120px;
    background: linear-gradient(45deg, var(--accent-yellow), var(--primary-gold));
    clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
    bottom: 20%;
    left: 20%;
    animation-delay: 10s;
}

@keyframes floatShape {
    0%, 100% {
        transform: translateY(0) rotate(0deg) scale(1);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-50px) rotate(180deg) scale(1.2);
        opacity: 0.3;
    }
}

/* Neon Grid Lines */
.neon-grid {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(255, 215, 0, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 215, 0, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 30s linear infinite;
}

@keyframes gridMove {
    from { transform: translate(0, 0); }
    to { transform: translate(50px, 50px); }
}

/* Particle Effects */
.particles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-gold);
    border-radius: 50%;
    animation: particleFloat 8s ease-in-out infinite;
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { left: 20%; animation-delay: 1s; }
.particle:nth-child(3) { left: 30%; animation-delay: 2s; }
.particle:nth-child(4) { left: 40%; animation-delay: 3s; }
.particle:nth-child(5) { left: 50%; animation-delay: 4s; }
.particle:nth-child(6) { left: 60%; animation-delay: 5s; }
.particle:nth-child(7) { left: 70%; animation-delay: 6s; }
.particle:nth-child(8) { left: 80%; animation-delay: 7s; }

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) scale(1);
        opacity: 0;
    }
}

/* Enhanced Glass Morphism for Cards */
.glass-card {
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 215, 0, 0.2) !important;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 20px rgba(255, 215, 0, 0.1) !important;
    border-radius: 20px !important;
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 215, 0, 0.1), 
        transparent);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Futuristic Buttons */
.btn-futuristic {
    background: linear-gradient(45deg, var(--primary-gold), var(--accent-orange)) !important;
    border: none !important;
    color: var(--primary-black) !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3) !important;
}

.btn-futuristic:hover {
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.5) !important;
    color: var(--primary-black) !important;
}

.btn-futuristic::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.3), 
        transparent);
    transition: left 0.5s ease;
}

.btn-futuristic:hover::before {
    left: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stars-layer-1, .stars-layer-2, .stars-layer-3 {
        animation-duration: 30s, 40s, 50s;
    }
    
    .geometric-shapes .shape {
        transform: scale(0.7);
    }
    
    .neon-grid {
        background-size: 30px 30px;
    }
}

/* Performance Optimization */
@media (prefers-reduced-motion: reduce) {
    .stars-layer-1, .stars-layer-2, .stars-layer-3,
    .geometric-shapes .shape, .neon-grid, .particle {
        animation: none;
    }
}
