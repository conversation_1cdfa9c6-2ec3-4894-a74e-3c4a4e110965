<?php
session_start();
require_once 'db_connect.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

if (!isset($_GET['driver_id'])) {
    header("Location: index.php");
    exit();
}

$driver_id = $_GET['driver_id'];
$currentMonth = isset($_GET['month']) ? intval($_GET['month']) : intval(date('m'));
$currentYear = isset($_GET['year']) ? intval($_GET['year']) : intval(date('Y'));

// Validate month and year
if ($currentMonth < 1 || $currentMonth > 12) {
    $currentMonth = intval(date('m'));
}
if ($currentYear < 2023 || $currentYear > 2100) {
    $currentYear = intval(date('Y'));
}

// Get driver details
$sql = "SELECT d.*, r.rickshaw_number 
        FROM drivers d 
        JOIN rickshaws r ON d.rickshaw_id = r.id 
        WHERE d.id = ? AND d.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ii", $driver_id, $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    header("Location: index.php");
    exit();
}

$driver = $result->fetch_assoc();

// Get attendance data for this driver for the selected month
$attendanceSql = "SELECT date, status, rent FROM attendance 
                 WHERE driver_id = ? AND user_id = ? AND MONTH(date) = ? AND YEAR(date) = ?
                 ORDER BY date";
$stmt = $conn->prepare($attendanceSql);
$stmt->bind_param("iiii", $driver_id, $_SESSION['user_id'], $currentMonth, $currentYear);
$stmt->execute();
$attendanceResult = $stmt->get_result();

$attendanceData = [];
$presentDays = 0;
$absentDays = 0;
$totalRent = 0;

while($row = $attendanceResult->fetch_assoc()) {
    $day = date('j', strtotime($row['date']));
    $attendanceData[$day] = [
        'status' => $row['status'],
        'rent' => $row['rent']
    ];
    
    if($row['status'] == 'উপস্থিত') {
        $presentDays++;
        $totalRent += $row['rent'];
    } else {
        $absentDays++;
    }
}

$daysInMonth = cal_days_in_month(CAL_GREGORIAN, $currentMonth, $currentYear);
$totalRecordedDays = $presentDays + $absentDays;
$noRecordDays = $daysInMonth - $totalRecordedDays;

// Process form submission for updating rent
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_rent'])) {
    $day = $_POST['day'];
    $rent = $_POST['rent'];
    $date = "$currentYear-$currentMonth-$day";
    
    $updateSql = "UPDATE attendance SET rent = ? WHERE driver_id = ? AND user_id = ? AND date = ?";
    $stmt = $conn->prepare($updateSql);
    $stmt->bind_param("diis", $rent, $driver_id, $_SESSION['user_id'], $date);
    
    if ($stmt->execute()) {
        // Refresh the page to show updated data
        header("Location: attendance.php?driver_id=$driver_id&month=$currentMonth&year=$currentYear");
        exit();
    }
}

// Get rent settings for reference
$regularSql = "SELECT amount FROM rent_settings WHERE day_type = 'regular' AND user_id = ?";
$stmt = $conn->prepare($regularSql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
$regularAmount = ($result->num_rows > 0) ? $result->fetch_assoc()['amount'] : $driver['rent'];

$fridaySql = "SELECT amount FROM rent_settings WHERE day_type = 'friday' AND user_id = ?";
$stmt = $conn->prepare($fridaySql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
$fridayAmount = ($result->num_rows > 0) ? $result->fetch_assoc()['amount'] : ($driver['rent'] + 50);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ড্রাইভার উপস্থিতি - <?php echo $driver['name']; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="futuristic-background.css">
    <style>
        :root {
            --purple-50: #f5f3fa;
            --purple-100: #ede9fe;
            --purple-200: #c4b5fd;
            --purple-300: #a78bfa;
            --purple-400: #8b5cf6;
            --purple-500: #7c3aed;
            --purple-600: #6d28d9;
            --purple-700: #3a006a;
            --purple-800: #2d003e;
            --purple-900: #1e0026;
            --purple-950: #12001a;
            --white: #fff;
        }
        
        body {
            font-family: 'Noto Sans Bengali', 'SolaimanLipi', Arial, sans-serif;
            background: linear-gradient(135deg, var(--purple-700) 0%, var(--purple-800) 100%);
            color: var(--white);
            padding-top: 70px;
        }
        
        /* Main Navbar - Fixed */
        .main-navbar {
            background-color: var(--purple-700);
            box-shadow: 0 4px 6px -1px rgba(58, 0, 106, 0.1), 0 2px 4px -1px rgba(58, 0, 106, 0.06);
            padding: 0.75rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1030;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--white);
        }
        .navbar-brand:hover {
            color: var(--purple-200);
        }
        .user-welcome {
            color: var(--white);
            font-weight: 600;
        }
        .logout-btn {
            background-color: var(--white);
            color: var(--purple-700);
            border: none;
            font-weight: 600;
            transition: all 0.2s;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
        }
        .logout-btn:hover {
            background-color: var(--purple-200);
            color: var(--purple-900);
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        .card {
            border: none;
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.2s;
            height: 100%;
            background: linear-gradient(135deg, var(--purple-800) 60%, var(--purple-700) 100%);
            color: var(--white);
            margin-bottom: 1.5rem;
        }
        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .card-header {
            padding: 1rem 1.25rem;
            border: none;
            font-weight: 600;
        }
        .card-header-red {
            background: linear-gradient(135deg, var(--purple-700), var(--purple-900));
            color: var(--white);
        }
        
        .card-body {
            padding: 20px;
        }
        
        .attendance-btn {
            width: 35px;
            height: 35px;
            padding: 0;
            line-height: 35px;
            text-align: center;
            margin: 2px;
            font-size: 0.875rem;
            border-radius: 0.375rem;
            transition: all 0.2s;
        }
        .attendance-btn:hover {
            transform: scale(1.1);
        }
        .present {
            background: linear-gradient(135deg, #10b981, #059669);
            color: var(--white);
            border: none;
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
        }
        .present:hover {
            background: linear-gradient(135deg, #059669, #047857);
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
        }
        .absent {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: var(--white);
            border: none;
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
        }
        .absent:hover {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(239, 68, 68, 0.4);
        }
        .no-record {
            background-color: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.7);
            border: 1px dashed rgba(255, 255, 255, 0.3);
        }
        .no-record:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: var(--white);
            border: 1px dashed rgba(255, 255, 255, 0.5);
            transform: scale(1.1);
        }
        .rent-display {
            font-size: 0.75rem;
            display: block;
            margin-top: 2px;
            color: var(--purple-200);
            font-weight: 600;
        }
        
        .summary-box {
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: transform 0.2s;
        }
        
        .summary-box:hover {
            transform: translateY(-3px);
        }
        
        .rent-display {
            font-size: 0.8rem;
            display: block;
            margin-top: 4px;
            font-weight: 600;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .table thead th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            border: none;
        }
        
        .badge {
            padding: 6px 10px;
            font-weight: 500;
        }
        
        .btn-action {
            border-radius: 50px;
            padding: 8px 20px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .month-selector {
            background-color: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .form-select {
            border-radius: 8px;
            padding: 10px;
            border: 1px solid #ddd;
        }
        
        .form-select:focus {
            box-shadow: 0 0 0 0.25rem rgba(63, 81, 181, 0.25);
            border-color: var(--primary-color);
        }
        
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        
        .modal-header {
            background-color: var(--primary-color);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        
        .modal-footer {
            border-top: none;
        }
        
        /* Dark mode toggle */
        .theme-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transition: all 0.3s;
        }
        
        .theme-toggle:hover {
            transform: scale(1.1);
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .card-body {
                padding: 1rem;
            }
            .attendance-btn {
                width: 30px;
                height: 30px;
                line-height: 30px;
                font-size: 0.75rem;
            }
            .btn {
                padding: 0.5rem 1rem;
            }
            .btn-sm {
                padding: 0.25rem 0.75rem;
                font-size: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <!-- Futuristic 3D Background -->
    <?php include 'futuristic-background.html'; ?>

    <!-- Main Navbar -->
    <nav class="navbar main-navbar">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                প্রচেষ্টা অটো রিকশা
            </a>
            <div class="ms-auto d-flex align-items-center">
                <span class="user-welcome me-3">স্বাগতম, <?php echo htmlspecialchars($_SESSION['username']); ?></span>
                <a href="logout.php" class="btn logout-btn btn-sm">
                    <i class="bi bi-box-arrow-right me-1"></i>
                    লগআউট
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4 fade-in">
        <!-- Page Header -->
        <div class="page-header fade-in">
            <h1>
                <i class="bi bi-calendar-week me-2"></i>
                ড্রাইভার উপস্থিতি
            </h1>
            <p>ড্রাইভার উপস্থিতি ও ভাড়া বিবরণ</p>
        </div>
        <!-- Page Title -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="text-center fw-bold">
                    <i class="bi bi-person-badge me-2"></i>
                    ড্রাইভার উপস্থিতি
                </h2>
                <p class="text-center text-muted">
                    <?php echo date('F Y', mktime(0, 0, 0, $currentMonth, 1, $currentYear)); ?>
                </p>
            </div>
        </div>
        
        <!-- Driver Info and Summary -->
        <div class="row mb-4">
            <!-- Driver Info -->
            <div class="col-md-6 mb-4 mb-md-0">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <i class="bi bi-person-circle me-2"></i>
                        ড্রাইভার তথ্য
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-4">
                            <div class="bg-light rounded-circle p-3 me-3">
                                <i class="bi bi-person-fill fs-1 text-primary"></i>
                            </div>
                            <div>
                                <h4 class="mb-0"><?php echo $driver['name']; ?></h4>
                                <p class="text-muted mb-0">রিকশা #<?php echo $driver['rickshaw_number']; ?></p>
                            </div>
                        </div>
                        
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-telephone-fill text-primary me-2"></i>
                                    <div>
                                        <small class="text-muted d-block">ফোন নম্বর</small>
                                        <span><?php echo $driver['phone']; ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-credit-card-2-front-fill text-primary me-2"></i>
                                    <div>
                                        <small class="text-muted d-block">এনআইডি</small>
                                        <span><?php echo $driver['nid']; ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-cash-stack text-primary me-2"></i>
                                    <div>
                                        <small class="text-muted d-block">ভাড়া</small>
                                        <span><?php echo $driver['rent']; ?> টাকা</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-calendar-date-fill text-primary me-2"></i>
                                    <div>
                                        <small class="text-muted d-block">যোগদানের তারিখ</small>
                                        <span><?php echo $driver['join_date']; ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Attendance Summary -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-info text-white">
                        <i class="bi bi-bar-chart-fill me-2"></i>
                        উপস্থিতি সারাংশ
                    </div>
                    <div class="card-body">
                        <div class="row g-3 mb-4">
                            <div class="col-6 col-md-3">
                                <div class="summary-box bg-success text-white">
                                    <h3><?php echo $presentDays; ?></h3>
                                    <p class="mb-0">উপস্থিত</p>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="summary-box bg-danger text-white">
                                    <h3><?php echo $absentDays; ?></h3>
                                    <p class="mb-0">অনুপস্থিত</p>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="summary-box bg-light">
                                    <h3><?php echo $noRecordDays; ?></h3>
                                    <p class="mb-0">রেকর্ড নেই</p>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="summary-box bg-warning">
                                    <h3><?php echo number_format($totalRent, 0); ?></h3>
                                    <p class="mb-0">মোট ভাড়া</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Month Selector -->
                        <form class="month-selector" method="get">
                            <input type="hidden" name="driver_id" value="<?php echo $driver_id; ?>">
                            <div class="row g-2">
                                <div class="col-md-5">
                                    <select name="month" class="form-select">
                                        <?php
                                        for($m=1; $m<=12; $m++) {
                                            $selected = ($m == $currentMonth) ? 'selected' : '';
                                            $monthName = date('F', mktime(0, 0, 0, $m, 1));
                                            echo "<option value='$m' $selected>$monthName</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <select name="year" class="form-select">
                                        <?php
                                        $startYear = 2023;
                                        $endYear = 2100;
                                        for($y=$startYear; $y<=$endYear; $y++) {
                                            $selected = ($y == $currentYear) ? 'selected' : '';
                                            echo "<option value='$y' $selected>$y</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-search me-1"></i> দেখুন
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Attendance Calendar -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="bi bi-calendar-month me-2"></i>
                মাসিক উপস্থিতি ক্যালেন্ডার
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <div class="d-flex flex-wrap justify-content-center">
                        <?php
                        for($day = 1; $day <= $daysInMonth; $day++) {
                            $date = "$currentYear-$currentMonth-$day";
                            $btnClass = 'no-record';
                            $btnText = $day;
                            $status = '';
                            $rentDisplay = '';
                            
                            if(isset($attendanceData[$day])) {
                                if($attendanceData[$day]['status'] == 'উপস্থিত') {
                                    $btnClass = 'present';
                                    $status = 'উপস্থিত';
                                    $rentDisplay = "<span class='rent-display'>{$attendanceData[$day]['rent']} ৳</span>";
                                } else {
                                    $btnClass = 'absent';
                                    $status = 'অনুপস্থিত';
                                }
                            }
                            
                            echo "<div class='text-center m-1'>";
                            echo "<a href='mark_attendance.php?driver_id=$driver_id&date=$date&month=$currentMonth&year=$currentYear' 
                                    class='btn attendance-btn $btnClass' 
                                    title='$day - $status'>$btnText</a>";
                            
                            if(!empty($rentDisplay)) {
                                echo $rentDisplay;
                                echo "<a href='#' data-bs-toggle='modal' data-bs-target='#editRentModal' 
                                      data-day='$day' data-rent='{$attendanceData[$day]['rent']}' 
                                      class='badge bg-secondary mt-1 d-block'>Edit</a>";
                            }
                            echo "</div>";
                        }
                        ?>
                    </div>
                </div>
                
                <div class="mt-4">
                    <div class="d-flex justify-content-center align-items-center mb-3">
                        <span class="btn attendance-btn present me-2"></span>
                        <span class="me-3">উপস্থিত</span>
                        <span class="btn attendance-btn absent me-2"></span>
                        <span class="me-3">অনুপস্থিত</span>
                        <span class="btn attendance-btn no-record me-2"></span>
                        <span>রেকর্ড নেই</span>
                    </div>
                    <p class="text-center text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        উপস্থিতি পরিবর্তন করতে তারিখে ক্লিক করুন
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Daily Rent Table -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <i class="bi bi-cash-coin me-2"></i>
                দৈনিক ভাড়া বিবরণ
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>তারিখ</th>
                                <th>উপস্থিতি</th>
                                <th>ভাড়া (টাকা)</th>
                                <th>অ্যাকশন</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $monthlyTotal = 0;
                            
                            // Sort by date
                            ksort($attendanceData);
                            
                            foreach($attendanceData as $day => $data) {
                                $date = date('Y-m-d', mktime(0, 0, 0, $currentMonth, $day, $currentYear));
                                $formattedDate = date('d F Y', mktime(0, 0, 0, $currentMonth, $day, $currentYear));
                                $status = $data['status'];
                                $rent = $data['rent'];
                                
                                if($status == 'উপস্থিত') {
                                    $monthlyTotal += $rent;
                                }
                                
                                echo "<tr>";
                                echo "<td>$formattedDate</td>";
                                echo "<td>";
                                if($status == 'উপস্থিত'):
                                    echo "<span class='badge bg-success'><i class='bi bi-check-circle-fill me-1'></i> উপস্থিত</span>";
                                else:
                                    echo "<span class='badge bg-danger'><i class='bi bi-x-circle-fill me-1'></i> অনুপস্থিত</span>";
                                endif;
                                echo "</td>";
                                echo "<td>" . ($status == 'উপস্থিত' ? $rent . ' ৳' : '-') . "</td>";
                                echo "<td>";
                                if($status == 'উপস্থিত') {
                                    echo "<button type='button' class='btn btn-sm btn-warning edit-rent-btn' 
                                           data-bs-toggle='modal' data-bs-target='#editRentModal'
                                           data-day='$day' data-rent='$rent'>
                                           Edit Rent
                                          </button>";
                                }
                                echo "</td>";
                                echo "</tr>";
                            }
                            
                            if(count($attendanceData) == 0) {
                                echo "<tr><td colspan='4' class='text-center'>কোন রেকর্ড পাওয়া যায়নি</td></tr>";
                            } else {
                                echo "<tr class='table-info'>";
                                echo "<td colspan='2'><strong>মোট ভাড়া</strong></td>";
                                echo "<td colspan='2'><strong>$monthlyTotal ৳</strong></td>";
                                echo "</tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="index.php?month=<?php echo $currentMonth; ?>&year=<?php echo $currentYear; ?>" class="btn btn-secondary">ড্যাশবোর্ডে ফিরে যান</a>
            <a href="rent_settings.php" class="btn btn-primary">ভাড়া সেটিংস</a>
        </div>
    </div>
    
    <!-- Edit Rent Modal -->
    <div class="modal fade" id="editRentModal" tabindex="-1" aria-labelledby="editRentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editRentModalLabel">Edit Rent</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="">
                    <div class="modal-body">
                        <input type="hidden" name="update_rent" value="1">
                        <input type="hidden" id="edit_day" name="day" value="">
                        <div class="mb-3">
                            <label for="edit_rent" class="form-label">ভাড়া (টাকা)</label>
                            <input type="number" step="0.01" class="form-control" id="edit_rent" name="rent" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                        <button type="submit" class="btn btn-primary">আপডেট করুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>© <?php echo date('Y'); ?> প্রচেষ্টা অটো রিকশা। সর্বস্বত্ব সংরক্ষিত।</p>
            <p class="developer-credit"> ©মাহতাব উদ্দিন আহমেদ।</p>
        </div>
    </footer>
    <style>
        .footer {
            background-color: var(--purple-900);
            color: var(--purple-200);
            padding: 1.5rem 0;
            text-align: center;
            margin-top: 3rem;
            border-top: 1px solid var(--purple-700);
        }
        .footer p {
            margin-bottom: 0;
            font-weight: 500;
        }
        .developer-credit {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: var(--purple-400);
        }
        .scroll-top {
            position: fixed;
            bottom: 1.25rem;
            right: 1.25rem;
            width: 40px;
            height: 40px;
            background-color: var(--purple-700);
            color: var(--white);
            border-radius: 9999px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0;
            transition: all 0.2s;
            z-index: 1000;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .scroll-top.show {
            opacity: 1;
        }
        .scroll-top:hover {
            background-color: var(--purple-900);
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>

    <!-- Scroll to top button -->
    <div class="scroll-top" id="scrollTop">
        <i class="bi bi-arrow-up"></i>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set up edit rent modal
        document.addEventListener('DOMContentLoaded', function() {
            const editRentModal = document.getElementById('editRentModal');
            if (editRentModal) {
                editRentModal.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const day = button.getAttribute('data-day');
                    const rent = button.getAttribute('data-rent');
                    const dayInput = document.getElementById('edit_day');
                    const rentInput = document.getElementById('edit_rent');
                    dayInput.value = day;
                    rentInput.value = rent;
                });
            }
        });
        // Show/hide scroll to top button
        window.addEventListener('scroll', function() {
            const scrollTopBtn = document.getElementById('scrollTop');
            if (window.pageYOffset > 300) {
                scrollTopBtn.classList.add('show');
            } else {
                scrollTopBtn.classList.remove('show');
            }
        });
        // Scroll to top functionality
        document.getElementById('scrollTop').addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>












