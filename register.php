<?php
session_start();
require_once 'db_connect.php';

// Check if user is already logged in
if(isset($_SESSION['user_id'])) {
    header("Location: index.php");
    exit();
}

$error = '';
$success = '';

// Process registration form
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = $_POST['username'];
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validate input
    if(empty($username) || empty($password) || empty($confirm_password)) {
        $error = "সব ঘর পূরণ করুন";
    } elseif($password != $confirm_password) {
        $error = "পাসওয়ার্ড মিলছে না";
    } else {
        // Check if username already exists
        $sql = "SELECT id FROM users WHERE username = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if($result->num_rows > 0) {
            $error = "ইউজারনেম ইতিমধ্যে ব্যবহৃত হয়েছে";
        } else {
            // Hash the password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert new user
            $sql = "INSERT INTO users (username, password) VALUES (?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ss", $username, $hashed_password);
            
            if($stmt->execute()) {
                $success = "রেজিস্ট্রেশন সফল হয়েছে। এখন লগইন করুন।";
            } else {
                $error = "রেজিস্ট্রেশন ব্যর্থ হয়েছে: " . $stmt->error;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>রেজিস্ট্রেশন - প্রচেষ্টা অটো রিকশা</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --purple-50: #f5f3fa;
            --purple-100: #ede9fe;
            --purple-200: #c4b5fd;
            --purple-300: #a78bfa;
            --purple-400: #8b5cf6;
            --purple-500: #7c3aed;
            --purple-600: #6d28d9;
            --purple-700: #3a006a;
            --purple-800: #2d003e;
            --purple-900: #1e0026;
            --purple-950: #12001a;
            --white: #fff;
        }
        body {
            font-family: 'Noto Sans Bengali', 'SolaimanLipi', Arial, sans-serif;
            background: linear-gradient(135deg, var(--purple-700) 0%, var(--purple-800) 100%);
            color: var(--white);
            padding-top: 70px;
        }
        .main-navbar {
            background-color: var(--purple-700);
            box-shadow: 0 4px 6px -1px rgba(58, 0, 106, 0.1), 0 2px 4px -1px rgba(58, 0, 106, 0.06);
            padding: 0.75rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1030;
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--white);
        }
        .navbar-brand:hover {
            color: var(--purple-200);
        }
        .footer {
            background-color: var(--purple-900);
            color: var(--purple-200);
            padding: 1.5rem 0;
            text-align: center;
            margin-top: 3rem;
            border-top: 1px solid var(--purple-700);
        }
        .footer p {
            margin-bottom: 0;
            font-weight: 500;
        }
        .developer-credit {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: var(--purple-400);
        }
        .register-container {
            max-width: 400px;
            margin: 80px auto;
        }
        .card {
            border: none;
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.2s;
            background: linear-gradient(135deg, var(--purple-800) 60%, var(--purple-700) 100%);
            color: var(--white);
            margin-bottom: 1.5rem;
        }
        .card-header {
            padding: 1rem 1.25rem;
            border: none;
            font-weight: 600;
            background: linear-gradient(135deg, var(--purple-700), var(--purple-900));
            color: var(--white);
        }
        .btn-primary {
            background: linear-gradient(135deg, var(--purple-700), var(--purple-800));
            border-color: var(--purple-800);
            color: var(--white);
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, var(--purple-800), var(--purple-700));
            border-color: var(--purple-900);
        }
        .btn-link {
            color: var(--purple-200);
        }
        .btn-link:hover {
            color: var(--purple-400);
        }
    </style>
</head>
<body>
    <nav class="navbar main-navbar">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                প্রচেষ্টা অটো রিকশা
            </a>
        </div>
    </nav>
    <div class="container">
        <div class="register-container">
            <div class="card">
                <div class="card-header text-center">
                    <span class="app-title">প্রচেষ্টা অটো রিকশা</span>
                </div>
                <div class="card-body p-4">
                    <h4 class="text-center mb-4">রেজিস্ট্রেশন করুন</h4>
                    
                    <?php if(!empty($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>
                    
                    <?php if(!empty($success)): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                    <?php endif; ?>
                    
                    <form method="post" action="">
                        <div class="mb-3">
                            <label for="username" class="form-label">ইউজারনেম</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">পাসওয়ার্ড</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">পাসওয়ার্ড নিশ্চিত করুন</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary">রেজিস্ট্রেশন</button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-3">
                        <a href="login.php" class="btn btn-link">ইতিমধ্যে অ্যাকাউন্ট আছে? লগইন করুন</a>
                    </div>
                </div>
            </div>
            
            <div class="footer">
                <p>Developed by Mahtab Uddin Ahmed</p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>