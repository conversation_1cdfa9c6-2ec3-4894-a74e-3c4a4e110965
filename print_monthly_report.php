<?php
require_once 'db_connect.php';

if (!isset($_GET['month']) || !isset($_GET['year'])) {
    header("Location: index.php");
    exit();
}

$currentMonth = intval($_GET['month']);
$currentYear = intval($_GET['year']);

// Get all rickshaws with driver info
$sql = "SELECT r.id, r.rickshaw_number, r.status, 
               d.id as driver_id, d.name, d.phone
        FROM rickshaws r 
        LEFT JOIN drivers d ON r.id = d.rickshaw_id 
        WHERE r.user_id = ?
        ORDER BY r.rickshaw_number";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();

// Calculate total earnings for the month
$totalEarningsSql = "SELECT SUM(rent) as total_earnings 
                     FROM attendance 
                     WHERE MONTH(date) = ? AND YEAR(date) = ? AND user_id = ?";
$stmt = $conn->prepare($totalEarningsSql);
$stmt->bind_param("iii", $currentMonth, $currentYear, $_SESSION['user_id']);
$stmt->execute();
$earningsResult = $stmt->get_result();
$totalEarnings = 0;
if ($earningsResult->num_rows > 0) {
    $totalEarnings = $earningsResult->fetch_assoc()['total_earnings'] ?: 0;
}

// Get earnings breakdown by rickshaw
$rickshawEarnings = [];
$rickshawAttendance = [];

if ($result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        if($row["driver_id"]) {
            // Get attendance data for this driver for the selected month
            $attendanceSql = "SELECT date, status, rent FROM attendance 
                             WHERE driver_id = ? AND user_id = ? AND MONTH(date) = ? AND YEAR(date) = ?";
            $stmt = $conn->prepare($attendanceSql);
            $stmt->bind_param("iiii", $row["driver_id"], $_SESSION['user_id'], $currentMonth, $currentYear);
            $stmt->execute();
            $attendanceResult = $stmt->get_result();
            
            $presentDays = 0;
            $absentDays = 0;
            $driverTotalRent = 0;
            
            while($attendance = $attendanceResult->fetch_assoc()) {
                if($attendance['status'] == 'উপস্থিত') {
                    $presentDays++;
                    $driverTotalRent += $attendance['rent'];
                } else {
                    $absentDays++;
                }
            }
            
            $rickshawEarnings[$row["id"]] = $driverTotalRent;
            $rickshawAttendance[$row["id"]] = [
                'present' => $presentDays,
                'absent' => $absentDays,
                'driver_name' => $row["name"],
                'rickshaw_number' => $row["rickshaw_number"]
            ];
        } else {
            $rickshawEarnings[$row["id"]] = 0;
            $rickshawAttendance[$row["id"]] = [
                'present' => 0,
                'absent' => 0,
                'driver_name' => 'কোন ড্রাইভার নেই',
                'rickshaw_number' => $row["rickshaw_number"]
            ];
        }
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>মাসিক আয় রিপোর্ট - <?php echo date('F Y', mktime(0, 0, 0, $currentMonth, 1, $currentYear)); ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Noto Sans Bengali', Arial, sans-serif;
            background-color: #fff;
            color: #333;
            padding: 20px;
        }
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #ddd;
            padding-bottom: 20px;
        }
        .report-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-subtitle {
            font-size: 18px;
            color: #666;
        }
        .summary-box {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        .earnings-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .earnings-table th, .earnings-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .earnings-table th {
            background-color: #f5f5f5;
        }
        .no-print {
            margin-bottom: 20px;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                padding: 0;
                margin: 0;
            }
            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="no-print">
            <button onclick="window.print()" class="btn btn-primary mb-3">
                <i class="bi bi-printer"></i> প্রিন্ট করুন
            </button>
            <a href="index.php" class="btn btn-secondary mb-3">ফিরে যান</a>
        </div>
        
        <div class="report-header">
            <div class="report-title">প্রচেষ্টা অটো রিকশা</div>
            <div class="report-subtitle">মাসিক আয় রিপোর্ট</div>
            <div><?php echo date('F Y', mktime(0, 0, 0, $currentMonth, 1, $currentYear)); ?></div>
        </div>
        
        <div class="summary-box">
            <h4>মাসিক মোট আয়</h4>
            <h2 class="text-success mt-3"><?php echo number_format($totalEarnings, 0); ?> টাকা</h2>
            <p class="text-muted">সকল রিকশা থেকে মোট আয়</p>
        </div>
        
        <h5 class="mb-3">রিকশা অনুযায়ী আয় বিবরণ</h5>
        <table class="earnings-table">
            <thead>
                <tr>
                    <th>রিকশা নম্বর</th>
                    <th>ড্রাইভার</th>
                    <th>উপস্থিত দিন</th>
                    <th>অনুপস্থিত দিন</th>
                    <th>মোট আয় (টাকা)</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $result->data_seek(0); // Reset the result pointer
                
                while($row = $result->fetch_assoc()) {
                    $rickshawId = $row["id"];
                    $rickshawNumber = $row["rickshaw_number"];
                    $driverName = isset($rickshawAttendance[$rickshawId]) ? $rickshawAttendance[$rickshawId]['driver_name'] : 'কোন ড্রাইভার নেই';
                    $presentDays = isset($rickshawAttendance[$rickshawId]) ? $rickshawAttendance[$rickshawId]['present'] : 0;
                    $absentDays = isset($rickshawAttendance[$rickshawId]) ? $rickshawAttendance[$rickshawId]['absent'] : 0;
                    $earnings = isset($rickshawEarnings[$rickshawId]) ? $rickshawEarnings[$rickshawId] : 0;
                    
                    echo "<tr>";
                    echo "<td>$rickshawNumber</td>";
                    echo "<td>$driverName</td>";
                    echo "<td>$presentDays</td>";
                    echo "<td>$absentDays</td>";
                    echo "<td>" . number_format($earnings, 0) . " ৳</td>";
                    echo "</tr>";
                }
                ?>
            </tbody>
            <tfoot>
                <tr>
                    <th colspan="4">মোট আয়</th>
                    <th><?php echo number_format($totalEarnings, 0); ?> টাকা</th>
                </tr>
            </tfoot>
        </table>
        
        <div class="mt-5 text-center">
            <p>প্রিন্ট তারিখ: <?php echo date('d/m/Y h:i A'); ?></p>
            <p>ডেভেলপ করেছেন মাহতাব উদ্দিন আহমেদ</p>
        </div>
    </div>
    
    <script>
        // Auto print when page loads
        window.onload = function() {
            // Uncomment the line below to automatically open print dialog
            // window.print();
        }
    </script>
</body>
</html>
