<?php
session_start();
require_once 'db_connect.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

$message = '';
$messageType = '';

// Handle rickshaw deletion
if(isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $rickshaw_id = $_GET['delete'];
    
    // Check if rickshaw has any drivers
    $checkSql = "SELECT COUNT(*) as driver_count FROM drivers WHERE rickshaw_id = ? AND user_id = ?";
    $stmt = $conn->prepare($checkSql);
    $stmt->bind_param("ii", $rickshaw_id, $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    if($row['driver_count'] > 0) {
        $message = "এই রিকশায় ড্রাইভার রয়েছে। প্রথমে ড্রাইভার সরান।";
        $messageType = 'danger';
    } else {
        // Delete rickshaw
        $deleteSql = "DELETE FROM rickshaws WHERE id = ? AND user_id = ?";
        $stmt = $conn->prepare($deleteSql);
        $stmt->bind_param("ii", $rickshaw_id, $_SESSION['user_id']);
        
        if($stmt->execute()) {
            $message = "রিকশা সফলভাবে মুছে ফেলা হয়েছে।";
            $messageType = 'success';
        } else {
            $message = "রিকশা মুছতে সমস্যা হয়েছে।";
            $messageType = 'danger';
        }
    }
}

// Handle status update
if(isset($_POST['update_status'])) {
    $rickshaw_id = $_POST['rickshaw_id'];
    $new_status = $_POST['status'];
    
    $updateSql = "UPDATE rickshaws SET status = ? WHERE id = ? AND user_id = ?";
    $stmt = $conn->prepare($updateSql);
    $stmt->bind_param("sii", $new_status, $rickshaw_id, $_SESSION['user_id']);
    
    if($stmt->execute()) {
        $message = "রিকশার অবস্থা আপডেট করা হয়েছে।";
        $messageType = 'success';
    } else {
        $message = "অবস্থা আপডেট করতে সমস্যা হয়েছে।";
        $messageType = 'danger';
    }
}

// Get all rickshaws with driver info
$sql = "SELECT r.id, r.rickshaw_number, r.status, 
               d.id as driver_id, d.name as driver_name
        FROM rickshaws r 
        LEFT JOIN drivers d ON r.id = d.rickshaw_id 
        WHERE r.user_id = ?
        ORDER BY r.rickshaw_number";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>রিকশা ব্যবস্থাপনা - প্রচেষ্টা অটো রিকশা</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&display=swap');
        
        :root {
            --purple-700: #3a006a;
            --purple-800: #2d003e;
            --purple-200: #e9d5ff;
            --white: #fff;
            --gray-100: #f3f4f6;
            --gray-500: #6b7280;
            --red-500: #ef4444;
            --green-500: #10b981;
            --yellow-500: #f59e0b;
        }

        body {
            font-family: 'Noto Sans Bengali', 'SolaimanLipi', Arial, sans-serif;
            background: linear-gradient(135deg, var(--purple-700) 0%, var(--purple-800) 100%);
            min-height: 100vh;
            color: var(--white);
            padding-top: 70px;
        }

        .main-navbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .navbar-brand {
            color: var(--white) !important;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 1rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            color: var(--white);
        }

        .card-header {
            background: linear-gradient(135deg, var(--purple-700), var(--purple-800));
            color: var(--white);
            font-weight: 600;
            border-radius: 1rem 1rem 0 0 !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .table {
            color: var(--white);
        }

        .table th {
            border-color: rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            font-weight: 600;
        }

        .table td {
            border-color: rgba(255, 255, 255, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--purple-700), var(--purple-800));
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--purple-800), var(--purple-700));
            transform: translateY(-2px);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--green-500), #059669);
            border: none;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--yellow-500), #d97706);
            border: none;
            color: var(--white);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--red-500), #dc2626);
            border: none;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: var(--white);
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-active {
            background: rgba(16, 185, 129, 0.2);
            color: #86efac;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .status-inactive {
            background: rgba(239, 68, 68, 0.2);
            color: #fca5a5;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #86efac;
            border-radius: 0.5rem;
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #fca5a5;
            border-radius: 0.5rem;
        }

        .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: var(--white);
            border-radius: 0.5rem;
        }

        .form-select:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--purple-200);
            box-shadow: 0 0 0 0.2rem rgba(233, 213, 255, 0.25);
            color: var(--white);
        }

        .form-select option {
            background: var(--purple-800);
            color: var(--white);
        }

        .fade-in {
            animation: fadeIn 0.6s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .footer {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .developer-credit {
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }

        @media (max-width: 768px) {
            .table-responsive {
                font-size: 0.875rem;
            }
            
            .btn {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar main-navbar">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-truck me-2"></i>প্রচেষ্টা অটো রিকশা
            </a>
        </div>
    </nav>

    <div class="container mt-4 fade-in">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-truck me-2"></i>রিকশা ব্যবস্থাপনা
                        </h5>
                        <a href="add_rickshaw.php" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus-circle me-1"></i>নতুন রিকশা যোগ করুন
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if(!empty($message)): ?>
                            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                                <?php echo $message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if($result->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>রিকশা নম্বর</th>
                                            <th>অবস্থা</th>
                                            <th>ড্রাইভার</th>
                                            <th>কার্যক্রম</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while($row = $result->fetch_assoc()): ?>
                                            <tr>
                                                <td>
                                                    <strong>রিকশা #<?php echo htmlspecialchars($row['rickshaw_number']); ?></strong>
                                                </td>
                                                <td>
                                                    <form method="post" class="d-inline">
                                                        <input type="hidden" name="rickshaw_id" value="<?php echo $row['id']; ?>">
                                                        <select name="status" class="form-select form-select-sm" style="width: auto; display: inline-block;" onchange="this.form.submit()">
                                                            <option value="Active" <?php echo ($row['status'] == 'Active') ? 'selected' : ''; ?>>সক্রিয়</option>
                                                            <option value="Inactive" <?php echo ($row['status'] == 'Inactive') ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                                                        </select>
                                                        <input type="hidden" name="update_status" value="1">
                                                    </form>
                                                </td>
                                                <td>
                                                    <?php if($row['driver_id']): ?>
                                                        <span class="text-success">
                                                            <i class="bi bi-person-check me-1"></i>
                                                            <?php echo htmlspecialchars($row['driver_name']); ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-warning">
                                                            <i class="bi bi-person-dash me-1"></i>
                                                            কোন ড্রাইভার নেই
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <?php if(!$row['driver_id']): ?>
                                                            <a href="add_driver.php?rickshaw_id=<?php echo $row['id']; ?>" class="btn btn-success btn-sm">
                                                                <i class="bi bi-person-plus me-1"></i>ড্রাইভার যোগ করুন
                                                            </a>
                                                        <?php endif; ?>
                                                        <a href="?delete=<?php echo $row['id']; ?>" class="btn btn-danger btn-sm" 
                                                           onclick="return confirm('আপনি কি নিশ্চিত যে এই রিকশা মুছে ফেলতে চান?')">
                                                            <i class="bi bi-trash me-1"></i>মুছুন
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="bi bi-truck" style="font-size: 3rem; opacity: 0.5;"></i>
                                <h5 class="mt-3">কোন রিকশা পাওয়া যায়নি</h5>
                                <p class="text-muted">প্রথমে একটি রিকশা যোগ করুন।</p>
                                <a href="add_rickshaw.php" class="btn btn-primary">
                                    <i class="bi bi-plus-circle me-1"></i>প্রথম রিকশা যোগ করুন
                                </a>
                            </div>
                        <?php endif; ?>

                        <div class="mt-3">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-1"></i>ড্যাশবোর্ডে ফিরে যান
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© <?php echo date('Y'); ?> প্রচেষ্টা অটো রিকশা। সর্বস্বত্ব সংরক্ষিত।</p>
            <p class="developer-credit">©মাহতাব উদ্দিন আহমেদ।</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
